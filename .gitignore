# Laravel
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
/storage/app/public/*
!/storage/app/public/.gitkeep
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log

# IDE
/.idea
/.vscode
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
laravel.log

# Docker
/docker/mysql/data/

# Build files
/public/build/
/public/mix-manifest.json

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.backup