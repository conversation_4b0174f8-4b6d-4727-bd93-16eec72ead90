# 🎯 LiquidLight Event Planner Management System

A comprehensive event management platform with CMS control, booking system, payment integration, and automated communication.

## 🚀 Quick Start

### Prerequisites
- PHP 8.1+
- Composer
- Node.js & npm
- MySQL 8.0+
- Docker (optional)

### Option 1: Traditional Setup

1. **Install PHP and Composer** (see SETUP_ENVIRONMENT.md)

2. **Clone and Setup**
```bash
git clone <repository-url>
cd liquidlight
composer install
npm install
```

3. **Environment Configuration**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Database Setup**
```bash
# Create database: liquidlight_events
php artisan migrate
php artisan db:seed
```

5. **Start Development Server**
```bash
php artisan serve
npm run dev
```

### Option 2: Docker Setup

1. **Start with Docker**
```bash
docker-compose up -d
```

2. **Install Dependencies**
```bash
docker-compose exec app composer install
docker-compose exec app npm install
```

3. **Setup Laravel**
```bash
docker-compose exec app php artisan key:generate
docker-compose exec app php artisan migrate
```

## 🔧 Development URLs

- **Application**: http://localhost:8000
- **Database**: localhost:3306
- **MailHog**: http://localhost:8025
- **Redis**: localhost:6379

## 📋 Features

### Core Features
- ✅ Event Management & Booking
- ✅ Multiple Payment Gateways (Razorpay, PhonePe, UPI)
- ✅ QR-coded E-tickets
- ✅ OTP-less Authentication
- ✅ Role-based Access Control
- ✅ CMS & Content Management
- ✅ SMS & WhatsApp Notifications
- ✅ PWA Support with Offline Access
- ✅ Real-time Analytics Dashboard

### Admin Roles
- **Super Admin**: Full system access
- **Manager**: Event & booking management
- **Ticket Booker**: Manual booking assistance
- **Scanner**: Ticket validation interface

## 🛠️ Tech Stack

- **Backend**: Laravel 10+ (PHP 8.1+)
- **Frontend**: Vue.js 3 + TailwindCSS + Alpine.js
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Queue**: Laravel Queues
- **Payment**: Razorpay, PhonePe, UPI
- **Communication**: Twilio, WhatsApp API
- **PWA**: Service Workers + Offline Support

## 📁 Project Structure

```
liquidlight/
├── app/                    # Laravel application
├── database/              # Migrations & seeders
├── resources/             # Views, assets, lang
├── public/                # Public assets
├── docker/                # Docker configuration
├── routes/                # Application routes
├── config/                # Configuration files
└── storage/               # File storage
```

## 🔐 Environment Variables

Key environment variables to configure:

```env
# Database
DB_DATABASE=liquidlight_events
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Payment Gateways
RAZORPAY_KEY=your_razorpay_key
RAZORPAY_SECRET=your_razorpay_secret
PHONEPE_MERCHANT_ID=your_phonepe_merchant_id

# Communication
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
WHATSAPP_API_KEY=your_whatsapp_api_key

# Email
MAIL_HOST=smtp.hostinger.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
```

## 📚 Documentation

- [Deployment Plan](DEPLOYMENT_PLAN.md) - Complete deployment guide
- [Quick Start Guide](QUICK_START_GUIDE.md) - Development setup
- [Setup Environment](SETUP_ENVIRONMENT.md) - Tool installation

## 🎯 Development Status

Current implementation status tracked in task management system.

## 📞 Support

For development questions or deployment assistance, refer to the documentation files or contact the development team.

---

**Ready to build amazing events!** 🎉