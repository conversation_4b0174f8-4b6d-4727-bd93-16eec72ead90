# 🛠️ Development Environment Setup for Windows

## 📋 Required Tools Installation

### 1. Install PHP (Required for <PERSON><PERSON>)

#### Option A: Using XAMPP (Recommended for beginners)
1. Download XAMPP from: https://www.apachefriends.org/download.html
2. Install XAMPP with PHP 8.1 or higher
3. Add PHP to your system PATH:
   - Open XAMPP installation directory (usually `C:\xampp`)
   - Copy the path to `php` folder (e.g., `C:\xampp\php`)
   - Add this path to your Windows PATH environment variable

#### Option B: Using PHP for Windows
1. Download PHP from: https://windows.php.net/download/
2. Extract to `C:\php`
3. Add `C:\php` to your system PATH
4. Copy `php.ini-development` to `php.ini`
5. Enable required extensions in `php.ini`:
   ```ini
   extension=curl
   extension=fileinfo
   extension=gd
   extension=mbstring
   extension=openssl
   extension=pdo_mysql
   extension=zip
   ```

### 2. Install Composer (PHP Package Manager)
1. Download Composer from: https://getcomposer.org/download/
2. Run the Windows installer
3. Verify installation: Open new command prompt and run `composer --version`

### 3. Install MySQL (Database)

#### Option A: Using XAMPP MySQL (if you installed XAMPP)
- MySQL is included with XAMPP
- Start MySQL service from XAMPP Control Panel

#### Option B: Standalone MySQL
1. Download MySQL from: https://dev.mysql.com/downloads/mysql/
2. Install MySQL Server
3. Remember your root password
4. Optionally install MySQL Workbench for GUI management

### 4. Verify Installation
Open a new command prompt and run these commands:
```bash
php --version
composer --version
mysql --version
node --version
npm --version
git --version
```

## 🚀 Quick Setup Commands

Once PHP and Composer are installed, run these commands in your project directory:

```bash
# Create Laravel project
composer create-project laravel/laravel liquidlight-event-planner

# Navigate to project
cd liquidlight-event-planner

# Install additional packages
composer require laravel/sanctum spatie/laravel-permission intervention/image barryvdh/laravel-dompdf

# Install frontend dependencies
npm install
npm install vue@next @vitejs/plugin-vue tailwindcss @tailwindcss/forms alpinejs

# Set up environment
cp .env.example .env
php artisan key:generate

# Create database and run migrations
php artisan migrate

# Start development server
php artisan serve
```

## 🔧 Alternative: Using Docker (Advanced)

If you prefer containerized development:

```bash
# Create docker-compose.yml for Laravel + MySQL
# This will be provided in the next step if needed
```

## 📝 Next Steps After Installation

1. Verify all tools are working
2. Create the Laravel project
3. Set up the database
4. Begin implementing the event management features

---

**Please install PHP and Composer first, then we can continue with the Laravel project setup!**