version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: liquidlight-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - liquidlight-network
    depends_on:
      - db
      - redis

  webserver:
    image: nginx:alpine
    container_name: liquidlight-webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - liquidlight-network
    depends_on:
      - app

  db:
    image: mysql:8.0
    container_name: liquidlight-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: liquidlight_events
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: liquidlight_user
      MYSQL_PASSWORD: liquidlight_password
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    ports:
      - "3306:3306"
    networks:
      - liquidlight-network

  redis:
    image: redis:alpine
    container_name: liquidlight-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - liquidlight-network

  mailhog:
    image: mailhog/mailhog
    container_name: liquidlight-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - liquidlight-network

networks:
  liquidlight-network:
    driver: bridge

volumes:
  dbdata:
    driver: local