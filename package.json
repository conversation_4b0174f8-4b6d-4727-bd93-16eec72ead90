{"name": "liquidlight-event-planner", "version": "1.0.0", "description": "LiquidLight Event Planner Management System", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.12", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.2", "postcss": "^8.4.31", "tailwindcss": "^3.2.1", "vite": "^4.0.0"}, "dependencies": {"vue": "^3.2.37", "alpinejs": "^3.10.2", "@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@vite-pwa/vite-plugin-pwa": "^0.14.0", "workbox-window": "^6.5.4", "qrcode": "^1.5.3", "chart.js": "^4.2.0", "vue-chartjs": "^5.2.0"}}